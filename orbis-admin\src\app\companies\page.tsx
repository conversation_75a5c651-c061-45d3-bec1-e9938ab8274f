'use client'

import { useState, useEffect } from 'react'
import { Building2, Users, ChevronDown, ChevronRight, Plus, Search, Settings, UserPlus, Shield, Eye, EyeOff } from 'lucide-react'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/components/ui/Toast'
import { CompanyService, UserService, Company, User, CreateCompanyData } from '@/lib/api'

export default function CompaniesPage() {
  const { user: currentUser } = useAuth()
  const { success, error: showError } = useToast()
  const [companies, setCompanies] = useState<Company[]>([])
  const [companyUsers, setCompanyUsers] = useState<Record<number, User[]>>({})
  const [expandedCompanies, setExpandedCompanies] = useState<Set<number>>(new Set())
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)

  const [createForm, setCreateForm] = useState({
    name: '',
    code: '',
    description: '',
    address: '',
    phone: '',
    email: '',
    website: ''
  })

  useEffect(() => {
    loadCompanies()
  }, [])

  const loadCompanies = async () => {
    try {
      setLoading(true)
      const companiesData = await CompanyService.getCompanies()
      setCompanies(companiesData)
    } catch (error) {
      showError('Erreur', 'Impossible de charger les entreprises')
      console.error('Error loading companies:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadCompanyUsers = async (companyId: number) => {
    try {
      const users = await CompanyService.getCompanyUsers(companyId)
      setCompanyUsers(prev => ({
        ...prev,
        [companyId]: users
      }))
    } catch (error) {
      showError('Erreur', 'Impossible de charger les utilisateurs de l\'entreprise')
      console.error('Error loading company users:', error)
    }
  }

  const toggleCompany = async (companyId: number) => {
    const newExpanded = new Set(expandedCompanies)

    if (expandedCompanies.has(companyId)) {
      newExpanded.delete(companyId)
    } else {
      newExpanded.add(companyId)
      // Charger les utilisateurs si pas encore chargés
      if (!companyUsers[companyId]) {
        await loadCompanyUsers(companyId)
      }
    }

    setExpandedCompanies(newExpanded)
  }

  const handleCreateCompany = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await CompanyService.createCompany(createForm)
      success('Succès', 'Entreprise créée avec succès')
      setShowCreateModal(false)
      setCreateForm({
        name: '',
        code: '',
        description: '',
        address: '',
        phone: '',
        email: '',
        website: ''
      })
      loadCompanies()
    } catch (error) {
      showError('Erreur', 'Impossible de créer l\'entreprise')
      console.error('Error creating company:', error)
    }
  }

  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des entreprises...</p>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Building2 className="w-8 h-8 text-green-600" />
              Gestion des Entreprises
            </h1>
            <p className="text-gray-600 mt-1">
              Gérez les entreprises clientes et leurs administrateurs
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Nouvelle Entreprise
          </button>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher une entreprise..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Companies List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Entreprises ({filteredCompanies.length})
            </h2>
          </div>

          <div className="divide-y divide-gray-200">
            {filteredCompanies.map((company) => (
              <div key={company.id} className="p-4">
                {/* Company Header */}
                <div
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                  onClick={() => toggleCompany(company.id)}
                >
                  <div className="flex items-center gap-3">
                    {expandedCompanies.has(company.id) ? (
                      <ChevronDown className="w-4 h-4 text-gray-400" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    )}
                    <Building2 className="w-5 h-5 text-green-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900">{company.name}</h3>
                      <p className="text-sm text-gray-500">Code: {company.code}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-500">
                      {company.user_count} admin(s)
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      company.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {company.is_active ? 'Actif' : 'Inactif'}
                    </span>
                  </div>
                </div>

                {/* Company Admins (when expanded) */}
                {expandedCompanies.has(company.id) && (
                  <div className="mt-4 ml-8 space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        Administrateurs de l'entreprise
                      </h4>
                      <button className="text-green-600 hover:text-green-700 text-sm flex items-center gap-1">
                        <UserPlus className="w-4 h-4" />
                        Ajouter Admin
                      </button>
                    </div>

                    {companyUsers[company.id] ? (
                      companyUsers[company.id].length > 0 ? (
                        <div className="space-y-2">
                          {companyUsers[company.id].map((user) => (
                            <div key={user.id} className="bg-gray-50 rounded-lg p-3 flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                  <Shield className="w-4 h-4 text-green-600" />
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">
                                    {user.first_name} {user.last_name}
                                  </p>
                                  <p className="text-sm text-gray-500">{user.email}</p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                  {user.role}
                                </span>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  user.is_active
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {user.is_active ? 'Actif' : 'Inactif'}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500 italic">Aucun administrateur assigné</p>
                      )
                    ) : (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                        Chargement des administrateurs...
                      </div>
                    )}
                  </div>
                )}
            ))}
          </div>
        </div>

        {/* Create Company Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">Créer une nouvelle entreprise</h3>
              <form onSubmit={handleCreateCompany} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nom de l'entreprise *
                  </label>
                  <input
                    type="text"
                    required
                    value={createForm.name}
                    onChange={(e) => setCreateForm({...createForm, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Code entreprise *
                  </label>
                  <input
                    type="text"
                    required
                    value={createForm.code}
                    onChange={(e) => setCreateForm({...createForm, code: e.target.value.toUpperCase()})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    Créer
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  )
}
