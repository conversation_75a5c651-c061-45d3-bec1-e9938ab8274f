#!/usr/bin/env python3
"""
Test de <NAME_EMAIL>
"""

import requests
import json

def test_login():
    """Test de <NAME_EMAIL>"""
    
    # URL de l'API
    login_url = "http://localhost:8000/api/v1/auth/login"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "orbis123!"
    }
    
    print("🔐 Test de <NAME_EMAIL>")
    print(f"URL: {login_url}")
    print(f"Données: {json.dumps(login_data, indent=2)}")
    print("-" * 50)
    
    try:
        # Faire la requête de login
        response = requests.post(
            login_url,
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ LOGIN RÉUSSI!")
            print(f"Access Token: {data.get('access_token', 'N/A')[:50]}...")
            print(f"User: {json.dumps(data.get('user', {}), indent=2)}")
            
            # Test d'un endpoint admin avec le token
            test_admin_endpoint(data.get('access_token'))
            
        else:
            print("❌ LOGIN ÉCHOUÉ!")
            try:
                error_data = response.json()
                print(f"Erreur: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Erreur brute: {response.text}")
                
    except Exception as e:
        print(f"❌ ERREUR DE CONNEXION: {e}")

def test_admin_endpoint(token):
    """Test d'un endpoint admin avec le token"""
    
    if not token:
        print("❌ Pas de token pour tester l'endpoint admin")
        return
        
    print("\n" + "="*50)
    print("🔧 Test de l'endpoint admin /api/v1/admin/users")
    
    admin_url = "http://localhost:8000/api/v1/admin/users"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(admin_url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ ENDPOINT ADMIN ACCESSIBLE!")
            print(f"Nombre d'utilisateurs: {len(data) if isinstance(data, list) else 'N/A'}")
            if isinstance(data, list) and len(data) > 0:
                print(f"Premier utilisateur: {json.dumps(data[0], indent=2)}")
        else:
            print("❌ ENDPOINT ADMIN INACCESSIBLE!")
            try:
                error_data = response.json()
                print(f"Erreur: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Erreur brute: {response.text}")
                
    except Exception as e:
        print(f"❌ ERREUR ENDPOINT ADMIN: {e}")

if __name__ == "__main__":
    test_login()
