'use client'

import { useState, useEffect } from 'react'
import { Users, Plus, Search, Mail, Clock, CheckCircle, XCircle, ArrowLeft, Send, Edit, Trash2, UserPlus, Shield, Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/components/ui/Toast'
import { UserService, User, CreateUserData, UpdateUserData } from '@/lib/api'

// Utilisation de l'interface User du service

interface Invitation {
  id: number
  email: string
  role: string
  expires_at: string
  is_pending: boolean
  is_expired: boolean
  accepted_at?: string
  rejected_at?: string
  invited_by_name: string
  created_at: string
}

export default function CompleteUsersPage() {
  const { user: currentUser } = useAuth()
  const { success, error: showError, warning } = useToast()
  const [users, setUsers] = useState<User[]>([])
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'users' | 'invitations'>('users')
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [showUserModal, setShowUserModal] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: 'user'
  })
  
  const [userForm, setUserForm] = useState({
    email: '',
    first_name: '',
    last_name: '',
    role: 'user',
    password: '',
    confirm_password: ''
  })

  useEffect(() => {
    fetchUsers()
    fetchInvitations()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const fetchedUsers = await UserService.listUsers()
      setUsers(fetchedUsers)
    } catch (error) {
      console.error('Error fetching users:', error)
      showError('Erreur', 'Impossible de charger les utilisateurs')
    } finally {
      setLoading(false)
    }
  }

  const fetchInvitations = async () => {
    try {
      // Mock data
      const mockInvitations: Invitation[] = [
        {
          id: 1,
          email: "<EMAIL>",
          role: "user",
          expires_at: "2024-12-31T23:59:59Z",
          is_pending: true,
          is_expired: false,
          invited_by_name: "Admin ORBIS",
          created_at: "2024-12-20T14:30:00Z"
        }
      ]
      setInvitations(mockInvitations)
    } catch (error) {
      console.error('Error fetching invitations:', error)
    }
  }

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault()

    if (userForm.password !== userForm.confirm_password) {
      showError('Erreur', 'Les mots de passe ne correspondent pas')
      return
    }

    try {
      if (editingUser) {
        // Mode modification
        const updateData: UpdateUserData = {
          email: userForm.email,
          first_name: userForm.first_name,
          last_name: userForm.last_name,
          role: userForm.role
        }

        const updatedUser = await UserService.updateUser(editingUser.id, updateData)
        setUsers(users.map(user => user.id === editingUser.id ? updatedUser : user))
        success('Succès', 'Utilisateur modifié avec succès!')
        setEditingUser(null)
      } else {
        // Mode création
        const createData: CreateUserData = {
          email: userForm.email,
          password: userForm.password,
          first_name: userForm.first_name,
          last_name: userForm.last_name,
          role: userForm.role
        }

        const newUser = await UserService.createUser(createData)
        setUsers([newUser, ...users])
        success('Succès', 'Utilisateur créé avec succès!')
      }

      handleCloseUserModal()
    } catch (error) {
      console.error('Error creating/updating user:', error)
      showError('Erreur', editingUser ? 'Erreur lors de la modification de l\'utilisateur' : 'Erreur lors de la création de l\'utilisateur')
    }
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setUserForm({
      email: user.email,
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      role: user.role,
      password: '',
      confirm_password: ''
    })
    setShowUserModal(true)
  }

  const handleCloseUserModal = () => {
    setShowUserModal(false)
    setEditingUser(null)
    setUserForm({ email: '', first_name: '', last_name: '', role: 'user', password: '', confirm_password: '' })
  }

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
      return
    }

    try {
      await UserService.deleteUser(userId)
      setUsers(users.filter(user => user.id !== userId))
      success('Succès', 'Utilisateur supprimé avec succès!')
    } catch (error) {
      console.error('Error deleting user:', error)
      showError('Erreur', 'Erreur lors de la suppression de l\'utilisateur')
    }
  }

  const handleToggleUserStatus = async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId)
      if (!user) return

      const updatedUser = await UserService.toggleUserStatus(userId, !user.is_active)
      setUsers(users.map(u => u.id === userId ? updatedUser : u))
      success('Succès', 'Statut de l\'utilisateur modifié avec succès!')
    } catch (error) {
      console.error('Error toggling user status:', error)
      showError('Erreur', 'Erreur lors de la modification du statut')
    }
  }

  const filteredUsers = users.filter(user =>
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.first_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.last_name || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredInvitations = invitations.filter(invitation =>
    invitation.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getRoleBadge = (role: string) => {
    const colors = {
      admin: 'bg-purple-100 text-purple-800',
      manager: 'bg-blue-100 text-blue-800',
      user: 'bg-gray-100 text-gray-800',
      viewer: 'bg-green-100 text-green-800'
    }
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[role as keyof typeof colors] || colors.user}`}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    )
  }

  const getStatusBadge = (user: User) => {
    if (user.is_active) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Actif
        </span>
      )
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Inactif
        </span>
      )
    }
  }

  return (
    <ProtectedRoute requireAdmin={true}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Link href="/" className="mr-4">
                  <ArrowLeft className="h-6 w-6 text-gray-600 hover:text-gray-900" />
                </Link>
                <Users className="h-8 w-8 text-green-600 mr-3" />
                <h1 className="text-2xl font-bold text-gray-900">Gestion des Utilisateurs</h1>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowInviteModal(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                >
                  <Mail className="h-4 w-4" />
                  Inviter
                </button>
                <button
                  onClick={() => {
                    setEditingUser(null)
                    setUserForm({ email: '', first_name: '', last_name: '', role: 'user', password: '', confirm_password: '' })
                    setShowUserModal(true)
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                >
                  <UserPlus className="h-4 w-4" />
                  Créer Utilisateur
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Tabs */}
          <div className="mb-6">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('users')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'users'
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Utilisateurs ({users.length})
                </button>
                <button
                  onClick={() => setActiveTab('invitations')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'invitations'
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Invitations ({invitations.length})
                </button>
              </nav>
            </div>
          </div>

          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder={activeTab === 'users' ? "Rechercher un utilisateur..." : "Rechercher une invitation..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full max-w-md border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Content based on active tab */}
          {activeTab === 'users' ? (
            // Users Table
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <li key={user.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {user.first_name} {user.last_name}
                            </p>
                            <p className="text-sm text-gray-500">{user.email}</p>
                            <p className="text-xs text-gray-400">
                              Créé le {new Date(user.created_at).toLocaleDateString('fr-FR')}
                              {user.last_sign_in_at && (
                                <> • Dernière connexion: {new Date(user.last_sign_in_at).toLocaleDateString('fr-FR')}</>
                              )}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getRoleBadge(user.role)}
                            {getStatusBadge(user)}
                            {user.is_verified && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <Shield className="w-3 h-3 mr-1" />
                                Vérifié
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ml-4 flex items-center space-x-2">
                        <button
                          onClick={() => handleToggleUserStatus(user.id)}
                          className={`text-sm font-medium ${
                            user.is_active 
                              ? 'text-red-600 hover:text-red-800' 
                              : 'text-green-600 hover:text-green-800'
                          }`}
                        >
                          {user.is_active ? 'Désactiver' : 'Activer'}
                        </button>
                        <button
                          onClick={() => handleEditUser(user)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-1"
                        >
                          <Edit className="h-3 w-3" />
                          Modifier
                        </button>
                        {user.id !== currentUser?.id && (
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-800 text-sm font-medium flex items-center gap-1"
                          >
                            <Trash2 className="h-3 w-3" />
                            Supprimer
                          </button>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            // Invitations content (existing code)
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <p className="p-4 text-gray-500">Contenu des invitations (code existant)</p>
            </div>
          )}
        </main>

        {/* User Modal */}
        {showUserModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold mb-4">
                {editingUser ? 'Modifier l\'utilisateur' : 'Créer un utilisateur'}
              </h3>
              <form onSubmit={handleCreateUser}>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Prénom</label>
                    <input
                      type="text"
                      required
                      value={userForm.first_name}
                      onChange={(e) => setUserForm({ ...userForm, first_name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nom</label>
                    <input
                      type="text"
                      required
                      value={userForm.last_name}
                      onChange={(e) => setUserForm({ ...userForm, last_name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    required
                    value={userForm.email}
                    onChange={(e) => setUserForm({ ...userForm, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rôle</label>
                  <select
                    value={userForm.role}
                    onChange={(e) => setUserForm({ ...userForm, role: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="user">Utilisateur</option>
                    <option value="manager">Manager</option>
                    <option value="admin">Administrateur</option>
                    <option value="viewer">Lecteur</option>
                  </select>
                </div>

                {!editingUser && (
                  <>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Mot de passe</label>
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          required
                          value={userForm.password}
                          onChange={(e) => setUserForm({ ...userForm, password: e.target.value })}
                          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          minLength={8}
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>
                    
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Confirmer le mot de passe</label>
                      <input
                        type={showPassword ? 'text' : 'password'}
                        required
                        value={userForm.confirm_password}
                        onChange={(e) => setUserForm({ ...userForm, confirm_password: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        minLength={8}
                      />
                    </div>
                  </>
                )}

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={handleCloseUserModal}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
                  >
                    {editingUser ? 'Modifier' : 'Créer'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  )
}
